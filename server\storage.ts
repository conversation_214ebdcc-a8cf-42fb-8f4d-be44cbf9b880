import {
  users, type User, type InsertUser,
  workflows, type Workflow, type InsertWorkflow,
  credentials, type Credential, type InsertCredential,
  workflowRuns, type WorkflowRun, type InsertWorkflowRun,
  nodeRuns, type NodeRun, type InsertNodeRun,
  logEntries, type LogEntry, type InsertLogEntry
} from "@shared/schema";

// Storage interface for CRUD operations
export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;

  // Workflow operations
  getWorkflows(): Promise<Workflow[]>;
  getWorkflow(id: number): Promise<Workflow | undefined>;
  createWorkflow(workflow: InsertWorkflow): Promise<Workflow>;
  updateWorkflow(id: number, workflow: Partial<InsertWorkflow>): Promise<Workflow | undefined>;
  deleteWorkflow(id: number): Promise<boolean>;
  getFavoriteWorkflows(): Promise<Workflow[]>;
  toggleWorkflowFavorite(id: number): Promise<Workflow | undefined>;

  // Credential operations
  getCredentials(): Promise<Credential[]>;
  getCredential(id: number): Promise<Credential | undefined>;
  createCredential(credential: InsertCredential): Promise<Credential>;
  updateCredential(id: number, credential: Partial<InsertCredential>): Promise<Credential | undefined>;
  deleteCredential(id: number): Promise<boolean>;

  // Workflow run operations
  getWorkflowRuns(workflowId?: number): Promise<WorkflowRun[]>;
  getWorkflowRun(id: number): Promise<WorkflowRun | undefined>;
  createWorkflowRun(run: InsertWorkflowRun): Promise<WorkflowRun>;
  updateWorkflowRun(id: number, run: Partial<WorkflowRun>): Promise<WorkflowRun | undefined>;

  // Node run operations
  getNodeRuns(workflowRunId: number): Promise<NodeRun[]>;
  getNodeRun(id: number): Promise<NodeRun | undefined>;
  createNodeRun(run: InsertNodeRun): Promise<NodeRun>;
  updateNodeRun(id: number, run: Partial<NodeRun>): Promise<NodeRun | undefined>;

  // Log entry operations
  createLogEntry(insertLogEntry: InsertLogEntry): Promise<LogEntry>;
  getLogEntries(workflowRunId: number, nodeRunId?: number): Promise<LogEntry[]>;
  getLogEntriesByLevel(workflowRunId: number, level: string): Promise<LogEntry[]>;
}

// Use DatabaseStorage instead of MemStorage for persistence
import { db } from './db';
import { eq, desc, and } from 'drizzle-orm';

export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  async getWorkflows(): Promise<Workflow[]> {
    return await db.select().from(workflows);
  }

  async getWorkflow(id: number): Promise<Workflow | undefined> {
    const [workflow] = await db.select().from(workflows).where(eq(workflows.id, id));
    return workflow || undefined;
  }

  async createWorkflow(insertWorkflow: InsertWorkflow): Promise<Workflow> {
    const [workflow] = await db
      .insert(workflows)
      .values(insertWorkflow)
      .returning();
    return workflow;
  }

  async updateWorkflow(id: number, updateData: Partial<InsertWorkflow>): Promise<Workflow | undefined> {
    const [workflow] = await db
      .update(workflows)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(workflows.id, id))
      .returning();
    return workflow || undefined;
  }

  async deleteWorkflow(id: number): Promise<boolean> {
    await db
      .delete(workflows)
      .where(eq(workflows.id, id));
    return true;
  }

  async getFavoriteWorkflows(): Promise<Workflow[]> {
    return await db
      .select()
      .from(workflows)
      .where(eq(workflows.isFavorite, true));
  }

  async toggleWorkflowFavorite(id: number): Promise<Workflow | undefined> {
    const workflow = await this.getWorkflow(id);
    if (!workflow) return undefined;

    const [updatedWorkflow] = await db
      .update(workflows)
      .set({
        isFavorite: !workflow.isFavorite,
        updatedAt: new Date()
      })
      .where(eq(workflows.id, id))
      .returning();
    return updatedWorkflow || undefined;
  }

  async getCredentials(): Promise<Credential[]> {
    return await db.select().from(credentials);
  }

  async getCredential(id: number): Promise<Credential | undefined> {
    const [credential] = await db.select().from(credentials).where(eq(credentials.id, id));
    return credential || undefined;
  }

  async createCredential(insertCredential: InsertCredential): Promise<Credential> {
    const [credential] = await db
      .insert(credentials)
      .values(insertCredential)
      .returning();
    return credential;
  }

  async updateCredential(id: number, updateData: Partial<InsertCredential>): Promise<Credential | undefined> {
    const [credential] = await db
      .update(credentials)
      .set(updateData)
      .where(eq(credentials.id, id))
      .returning();
    return credential || undefined;
  }

  async deleteCredential(id: number): Promise<boolean> {
    await db
      .delete(credentials)
      .where(eq(credentials.id, id));
    return true;
  }

  async getWorkflowRuns(workflowId?: number): Promise<WorkflowRun[]> {
    if (workflowId) {
      return await db
        .select()
        .from(workflowRuns)
        .where(eq(workflowRuns.workflowId, workflowId))
        .orderBy(desc(workflowRuns.startTime));
    }

    return await db
      .select()
      .from(workflowRuns)
      .orderBy(desc(workflowRuns.startTime));
  }

  async getWorkflowRun(id: number): Promise<WorkflowRun | undefined> {
    const [run] = await db.select().from(workflowRuns).where(eq(workflowRuns.id, id));
    return run || undefined;
  }

  async createWorkflowRun(insertRun: InsertWorkflowRun): Promise<WorkflowRun> {
    const [run] = await db
      .insert(workflowRuns)
      .values({
        ...insertRun,
        input: insertRun.input || {},
        logs: {}
      })
      .returning();
    return run;
  }

  async updateWorkflowRun(id: number, updateData: Partial<WorkflowRun>): Promise<WorkflowRun | undefined> {
    const [run] = await db
      .update(workflowRuns)
      .set(updateData)
      .where(eq(workflowRuns.id, id))
      .returning();
    return run || undefined;
  }

  async getNodeRuns(workflowRunId: number): Promise<NodeRun[]> {
    return await db
      .select()
      .from(nodeRuns)
      .where(eq(nodeRuns.workflowRunId, workflowRunId));
  }

  async getNodeRun(id: number): Promise<NodeRun | undefined> {
    const [nodeRun] = await db.select().from(nodeRuns).where(eq(nodeRuns.id, id));
    return nodeRun || undefined;
  }

  async createNodeRun(insertRun: InsertNodeRun): Promise<NodeRun> {
    const [run] = await db
      .insert(nodeRuns)
      .values({
        ...insertRun,
        input: insertRun.input || {},
        output: insertRun.output || {}
      })
      .returning();
    return run;
  }

  async updateNodeRun(id: number, updateData: Partial<NodeRun>): Promise<NodeRun | undefined> {
    const [run] = await db
      .update(nodeRuns)
      .set(updateData)
      .where(eq(nodeRuns.id, id))
      .returning();
    return run || undefined;
  }

  async createLogEntry(insertLogEntry: InsertLogEntry): Promise<LogEntry> {
    const [logEntry] = await db
      .insert(logEntries)
      .values({
        ...insertLogEntry,
        details: insertLogEntry.details || {}
      })
      .returning();
    return logEntry;
  }

  async getLogEntries(workflowRunId: number, nodeRunId?: number): Promise<LogEntry[]> {
    if (nodeRunId !== undefined) {
      return await db
        .select()
        .from(logEntries)
        .where(and(
          eq(logEntries.workflowRunId, workflowRunId),
          eq(logEntries.nodeRunId, nodeRunId)
        ))
        .orderBy(desc(logEntries.timestamp));
    }

    return await db
      .select()
      .from(logEntries)
      .where(eq(logEntries.workflowRunId, workflowRunId))
      .orderBy(desc(logEntries.timestamp));
  }

  async getLogEntriesByLevel(workflowRunId: number, level: string): Promise<LogEntry[]> {
    return await db
      .select()
      .from(logEntries)
      .where(and(
        eq(logEntries.workflowRunId, workflowRunId),
        eq(logEntries.level, level)
      ))
      .orderBy(desc(logEntries.timestamp));
  }
}

export const storage = new DatabaseStorage();
