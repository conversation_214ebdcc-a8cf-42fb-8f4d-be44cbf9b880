import React from 'react';
import { NodeProps } from 'reactflow';
import BaseNode from './BaseNode';
import { Bot } from 'lucide-react';

const AgentNode: React.FC<NodeProps> = ({ id, data, selected }) => {
  return (
    <BaseNode
      id={id}
      data={data}
      icon={<Bot size={16} />}
      color="#107C10"
      hasInput={true}
      hasOutput={true}
      selected={selected}
    >
      <div className="mt-2 p-2 bg-neutral-50 dark:bg-neutral-900 rounded text-xs font-mono line-clamp-3">
        {data.systemPrompt || 'No system prompt defined'}
      </div>

      <div className="mt-2 flex items-center text-xs text-neutral-600 dark:text-neutral-400">
        <span className="px-1.5 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 rounded">
          {data.model || 'No model selected'}
        </span>
        <span className="ml-auto">
          T: {data.temperature || 0.7} | Max: {data.maxTokens || 1000}
        </span>
      </div>

      <div className="mt-1 flex items-center text-xs text-neutral-600 dark:text-neutral-400">
        <span>
          {data.outputFormat === 'json' ? 'JSON output' : 'Text output'}
        </span>
      </div>
    </BaseNode>
  );
};

export default AgentNode;
