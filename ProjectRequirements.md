Requirements
1. User Access
R1.1: The application should provide direct access or a simple mechanism for a single, predefined user.
2. Workflow Creation & Management
R2.1: The user can create new, named workflows.
R2.2: The user can view a list of their existing workflows.
R2.3: The user can edit existing workflows (modify nodes, connections, settings).
R2.4: The user can delete workflows.
R2.5: Workflows shall have a visual interface (e.g., drag-and-drop canvas) for adding and connecting nodes.
R2.6: Connections between nodes shall define the flow of data.
R2.7: The system must allow saving the workflow state (e.g., published, draft).
R2.8: The user can mark/unmark a workflow as a favorite.
R2.9: The user can filter or view a list of their favorite workflows.
3. Node Types & Functionality
R3.1: General Node Properties
R3.1.1: Each node has a unique identifier within a workflow.
R3.1.2: Each node has a configurable name/label.
R3.1.3: Nodes can be connected to one or more subsequent nodes for data piping.
R3.2: Input Node
R3.2.1: Starts the workflow.
R3.2.2: The user can define the structure/schema of the input data.
R3.2.3: Sends the input data to the next connected node(s).
R3.3: API Trigger Node
R3.3.1: Starts the workflow when an HTTP request is received at a unique endpoint generated for this node.
R3.3.2: The input for the workflow is the payload of the incoming HTTP request.
R3.3.3: The user can specify allowed HTTP methods (GET, POST, etc.).
R3.3.4: Option to secure the API trigger (e.g., API key).
R3.4: Prompt Node
R3.4.1: Takes input from a previous node or a manually configured static input.
R3.4.2: The user can specify a prompt template, allowing for dynamic insertion of input data.
R3.4.3: The user can specify the AI model to use (from saved credentials).
R3.4.4: The user can specify if the expected response is simple text or a structured format (e.g., JSON).
R3.4.4.1: If structured, the user can define the desired output schema or provide an example.
R3.4.5: Runs the prompt with the input against the selected LLM.
R3.4.6: Outputs the result (simple text or structured data).
R3.4.7: Can pipe the result to the next node(s).
R3.5: Agent Node
R3.5.1: Takes input from a previous node or a manually configured static input.
R3.5.2: Has a "node prompt" or system prompt that defines its role, capabilities, or instructions.
R3.5.3: Uses the prompt and processes the input.
R3.5.4: The user can specify the AI model to use (from saved credentials).
R3.5.5: The user can specify if the expected response is simple text or a structured format.
R3.5.6: Outputs the result.
R3.5.7: Can pipe the result to the next node(s).
R3.6: Custom Nodes
R3.6.1: Allow the user to create custom nodes and define their workings.
R3.6.2: Provide an interface/SDK for the user to define:
Node name and icon.
Input parameters (name, type, required).
Output parameters (name, type).
The core logic of the node.
Configuration UI for the node within the workflow editor.
R3.6.3: The user can save and reuse their custom nodes across workflows.
4. Credential Management
R4.1: Allow the user to save credentials for AI LLM models.
R4.2: Securely store API keys and other sensitive credentials.
R4.3: The user can add, view (partially masked), edit, and delete credentials for different LLM providers.
R4.4: Nodes requiring AI models shall allow the user to select from their saved credentials.
R4.5: Utilize Vercel AI SDK with Google Gemini and OpenRouter provider.
R4.5.1: Integrate the Vercel AI SDK for interactions with LLMs.
R4.5.2: Support Google Gemini models.
R4.5.3: Support models available through OpenRouter.
5. Workflow Execution & Monitoring
R5.1: The user can manually trigger a workflow run.
R5.2: API Trigger nodes shall execute workflows upon receiving a valid request.
R5.3: Allow the user to view the execution status of nodes (e.g., pending, running, success, error).
R5.4: Provide real-time (or near real-time) updates on the workflow canvas during execution.
R5.5: Allow the user to inspect the input, output, and errors for each node in a run.
R5.5.1: Data should be presented in a readable format.
6. Run Tracking & History
R6.1: Track all workflow runs.
R6.2: Store a history of all workflow executions.
R6.3: For each run, store: Workflow ID, start/end time, overall status, trigger type, initial input, and detailed logs for each node (input, output, errors, duration).
R6.4: The user can view a list of past runs for a specific workflow.
R6.5: The user can drill down into the details of a specific run.
7. Analytics
R7.1: Store analytics data.
R7.2: Analytics to include: total runs per workflow, success/failure rates per workflow, average run duration per workflow, error occurrences, and messages.
R7.3: Provide a dashboard or view to visualize these analytics.
R7.4: Allow filtering analytics by time period.
8. Technical & Non-Functional Requirements
R8.1: UI/UX:
Intuitive and user-friendly interface, suitable for a developer.
Responsive design for desktop use.
R8.2: Performance:
Workflow execution should be efficient.
The UI should be responsive for managing approximately 15-20 workflows.
R8.3: Scalability:
The system must efficiently handle approximately 15-20 workflows and their associated execution data for a single user.
R8.4: Security:
Protect LLM credentials.
If deployed, the application access should be secured.
The developer user is responsible for the security of their custom node code.
R8.5: Error Handling:
Graceful error handling throughout the application.
Clear error messages to the user.