import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { z } from "zod";
import {
  insertWorkflowSchema,
  insertCredentialSchema,
  insertWorkflowRunSchema,
  insertNodeRunSchema,
  insertLogEntrySchema
} from "@shared/schema";
import { WorkflowExecutor } from "./workflow-executor";

export async function registerRoutes(app: Express): Promise<Server> {
  // API routes prefix
  const apiPrefix = "/api";

  // Workflows
  app.get(`${apiPrefix}/workflows`, async (req: Request, res: Response) => {
    try {
      const favorites = req.query.favorites === 'true';
      const workflows = favorites
        ? await storage.getFavoriteWorkflows()
        : await storage.getWorkflows();
      res.json(workflows);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workflows" });
    }
  });

  app.get(`${apiPrefix}/workflows/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const workflow = await storage.getWorkflow(id);
      if (!workflow) {
        return res.status(404).json({ error: "Workflow not found" });
      }
      res.json(workflow);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workflow" });
    }
  });

  app.post(`${apiPrefix}/workflows`, async (req: Request, res: Response) => {
    try {
      const validatedData = insertWorkflowSchema.parse(req.body);
      const workflow = await storage.createWorkflow(validatedData);
      res.status(201).json(workflow);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      res.status(500).json({ error: "Failed to create workflow" });
    }
  });

  app.patch(`${apiPrefix}/workflows/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const validatedData = insertWorkflowSchema.partial().parse(req.body);
      const workflow = await storage.updateWorkflow(id, validatedData);
      if (!workflow) {
        return res.status(404).json({ error: "Workflow not found" });
      }
      res.json(workflow);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      res.status(500).json({ error: "Failed to update workflow" });
    }
  });

  app.delete(`${apiPrefix}/workflows/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteWorkflow(id);
      if (!success) {
        return res.status(404).json({ error: "Workflow not found" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete workflow" });
    }
  });

  app.post(`${apiPrefix}/workflows/:id/favorite`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const workflow = await storage.toggleWorkflowFavorite(id);
      if (!workflow) {
        return res.status(404).json({ error: "Workflow not found" });
      }
      res.json(workflow);
    } catch (error) {
      res.status(500).json({ error: "Failed to toggle workflow favorite status" });
    }
  });

  // Credentials
  app.get(`${apiPrefix}/credentials`, async (_req: Request, res: Response) => {
    try {
      const credentials = await storage.getCredentials();
      res.json(credentials);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch credentials" });
    }
  });

  app.get(`${apiPrefix}/credentials/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const credential = await storage.getCredential(id);
      if (!credential) {
        return res.status(404).json({ error: "Credential not found" });
      }

      // Mask API key for security
      const maskedCredential = {
        ...credential,
        apiKey: credential.apiKey.substring(0, 4) + "..." + credential.apiKey.substring(credential.apiKey.length - 4)
      };

      res.json(maskedCredential);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch credential" });
    }
  });

  app.post(`${apiPrefix}/credentials`, async (req: Request, res: Response) => {
    try {
      const validatedData = insertCredentialSchema.parse(req.body);
      const credential = await storage.createCredential(validatedData);
      res.status(201).json(credential);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      res.status(500).json({ error: "Failed to create credential" });
    }
  });

  app.patch(`${apiPrefix}/credentials/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const validatedData = insertCredentialSchema.partial().parse(req.body);
      const credential = await storage.updateCredential(id, validatedData);
      if (!credential) {
        return res.status(404).json({ error: "Credential not found" });
      }
      res.json(credential);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      res.status(500).json({ error: "Failed to update credential" });
    }
  });

  app.delete(`${apiPrefix}/credentials/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteCredential(id);
      if (!success) {
        return res.status(404).json({ error: "Credential not found" });
      }
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete credential" });
    }
  });

  // Workflow Runs
  app.get(`${apiPrefix}/workflow-runs`, async (req: Request, res: Response) => {
    try {
      const workflowId = req.query.workflowId ? parseInt(req.query.workflowId as string) : undefined;
      const runs = await storage.getWorkflowRuns(workflowId);
      res.json(runs);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workflow runs" });
    }
  });

  app.get(`${apiPrefix}/workflow-runs/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const run = await storage.getWorkflowRun(id);
      if (!run) {
        return res.status(404).json({ error: "Workflow run not found" });
      }
      res.json(run);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch workflow run" });
    }
  });

  app.post(`${apiPrefix}/workflow-runs`, async (req: Request, res: Response) => {
    try {
      const validatedData = insertWorkflowRunSchema.parse(req.body);
      const run = await storage.createWorkflowRun(validatedData);

      // Start workflow execution asynchronously
      const workflowExecutor = new WorkflowExecutor();
      workflowExecutor.executeWorkflow(run.id).catch((error: any) => {
        console.error(`Workflow execution failed for run ${run.id}:`, error);
      });

      res.status(201).json(run);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      res.status(500).json({ error: "Failed to create workflow run" });
    }
  });

  app.patch(`${apiPrefix}/workflow-runs/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const run = await storage.updateWorkflowRun(id, req.body);
      if (!run) {
        return res.status(404).json({ error: "Workflow run not found" });
      }
      res.json(run);
    } catch (error) {
      res.status(500).json({ error: "Failed to update workflow run" });
    }
  });

  // Node Runs
  app.get(`${apiPrefix}/workflow-runs/:runId/node-runs`, async (req: Request, res: Response) => {
    try {
      const runId = parseInt(req.params.runId);
      const nodeRuns = await storage.getNodeRuns(runId);
      res.json(nodeRuns);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch node runs" });
    }
  });

  app.post(`${apiPrefix}/node-runs`, async (req: Request, res: Response) => {
    try {
      const validatedData = insertNodeRunSchema.parse(req.body);
      const nodeRun = await storage.createNodeRun(validatedData);
      res.status(201).json(nodeRun);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: error.errors });
      }
      res.status(500).json({ error: "Failed to create node run" });
    }
  });

  app.patch(`${apiPrefix}/node-runs/:id`, async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const nodeRun = await storage.updateNodeRun(id, req.body);
      if (!nodeRun) {
        return res.status(404).json({ error: "Node run not found" });
      }
      res.json(nodeRun);
    } catch (error) {
      res.status(500).json({ error: "Failed to update node run" });
    }
  });

  // Log entry routes
  app.get(`${apiPrefix}/workflow-runs/:workflowRunId/logs`, async (req: Request, res: Response) => {
    try {
      const workflowRunId = parseInt(req.params.workflowRunId);
      const nodeRunId = req.query.nodeRunId ? parseInt(req.query.nodeRunId as string) : undefined;
      const level = req.query.level as string;

      let logs;
      if (level) {
        logs = await storage.getLogEntriesByLevel(workflowRunId, level);
      } else {
        logs = await storage.getLogEntries(workflowRunId, nodeRunId);
      }

      res.json(logs);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch log entries" });
    }
  });

  app.get(`${apiPrefix}/node-runs/:nodeRunId/logs`, async (req: Request, res: Response) => {
    try {
      const nodeRunId = parseInt(req.params.nodeRunId);

      // Get the node run to find the workflow run ID
      const nodeRun = await storage.getNodeRun(nodeRunId);

      if (!nodeRun) {
        return res.status(404).json({ error: "Node run not found" });
      }

      const logs = await storage.getLogEntries(nodeRun.workflowRunId, nodeRunId);
      res.json(logs);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch node run logs" });
    }
  });

  // API Trigger endpoints - dynamic endpoints for workflow triggers
  app.all('/api/trigger/:workflowId/:nodeId', async (req: Request, res: Response) => {
    try {
      const { workflowId, nodeId } = req.params;

      // Get the workflow
      const workflow = await storage.getWorkflow(parseInt(workflowId));
      if (!workflow) {
        return res.status(404).json({ error: 'Workflow not found' });
      }

      // Find the API trigger node
      const nodes = workflow.nodes as Record<string, any>;
      const node = nodes[nodeId];
      if (!node || node.type !== 'api') {
        return res.status(404).json({ error: 'API trigger node not found' });
      }

      // Check if the HTTP method is allowed
      const allowedMethods = node.data.method ? [node.data.method] : ['GET', 'POST', 'PUT', 'DELETE'];
      if (!allowedMethods.includes(req.method)) {
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
      }

      // Check API key authentication if required
      if (node.data.authType === 'apiKey') {
        const apiKeyHeader = node.data.apiKeyHeader || 'x-api-key';
        const providedKey = req.headers[apiKeyHeader.toLowerCase()];

        if (node.data.credentialId) {
          const credential = await storage.getCredential(node.data.credentialId);
          if (!credential || providedKey !== credential.apiKey) {
            return res.status(401).json({ error: 'Invalid API key' });
          }
        }
      }

      // Create workflow run with API trigger
      const workflowRun = await storage.createWorkflowRun({
        workflowId: parseInt(workflowId),
        status: 'pending',
        triggerType: 'api',
        input: {
          method: req.method,
          headers: req.headers,
          query: req.query,
          body: req.body,
          params: req.params
        }
      });

      // Start workflow execution asynchronously
      const workflowExecutor = new WorkflowExecutor();
      workflowExecutor.executeWorkflow(workflowRun.id).catch((error: any) => {
        console.error(`API triggered workflow execution failed for run ${workflowRun.id}:`, error);
      });

      res.json({
        success: true,
        runId: workflowRun.id,
        message: 'Workflow triggered successfully'
      });
    } catch (error) {
      console.error('API trigger error:', error);
      res.status(500).json({ error: 'Failed to trigger workflow' });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
