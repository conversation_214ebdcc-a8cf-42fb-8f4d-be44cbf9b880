import { pgTable, text, serial, integer, boolean, jsonb, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// User schema (simplified for single user system)
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

// Workflow schema
export const workflows = pgTable("workflows", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull().default(''),
  nodes: jsonb("nodes").notNull().default({}),
  edges: jsonb("edges").notNull().default({}),
  status: text("status").notNull().default("draft"), // draft, published
  isFavorite: boolean("is_favorite").notNull().default(false),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const insertWorkflowSchema = createInsertSchema(workflows).pick({
  name: true,
  description: true,
  nodes: true,
  edges: true,
  status: true,
  isFavorite: true,
});

// Credentials schema
export const credentials = pgTable("credentials", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  provider: text("provider").notNull(), // google, openrouter, etc.
  apiKey: text("api_key").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

export const insertCredentialSchema = createInsertSchema(credentials).pick({
  name: true,
  provider: true,
  apiKey: true,
});

// Workflow runs schema
export const workflowRuns = pgTable("workflow_runs", {
  id: serial("id").primaryKey(),
  workflowId: integer("workflow_id").notNull().references(() => workflows.id),
  status: text("status").notNull(), // pending, running, completed, failed
  startTime: timestamp("start_time").notNull().defaultNow(),
  endTime: timestamp("end_time"),
  triggerType: text("trigger_type").notNull(), // manual, api
  input: jsonb("input").notNull().default({}),
  logs: jsonb("logs").notNull().default({}),
  executionDuration: integer("execution_duration"), // in milliseconds
  totalNodes: integer("total_nodes"),
  completedNodes: integer("completed_nodes"),
  failedNodes: integer("failed_nodes"),
  errorMessage: text("error_message"),
  stackTrace: text("stack_trace"),
});

export const insertWorkflowRunSchema = createInsertSchema(workflowRuns).pick({
  workflowId: true,
  status: true,
  triggerType: true,
  input: true,
});

// Node run logs schema
export const nodeRuns = pgTable("node_runs", {
  id: serial("id").primaryKey(),
  workflowRunId: integer("workflow_run_id").notNull().references(() => workflowRuns.id),
  nodeId: text("node_id").notNull(),
  nodeType: text("node_type").notNull(),
  nodeName: text("node_name"),
  status: text("status").notNull(), // pending, running, completed, failed
  startTime: timestamp("start_time").notNull().defaultNow(),
  endTime: timestamp("end_time"),
  executionDuration: integer("execution_duration"), // in milliseconds
  input: jsonb("input").notNull().default({}),
  output: jsonb("output").notNull().default({}),
  error: text("error"),
  stackTrace: text("stack_trace"),
  retryCount: integer("retry_count").default(0),
  memoryUsage: integer("memory_usage"), // in bytes
  cpuTime: integer("cpu_time"), // in milliseconds
});

export const insertNodeRunSchema = createInsertSchema(nodeRuns).pick({
  workflowRunId: true,
  nodeId: true,
  nodeType: true,
  nodeName: true,
  status: true,
  input: true,
  output: true,
  error: true,
  stackTrace: true,
  retryCount: true,
  memoryUsage: true,
  cpuTime: true,
});

// Detailed log entries schema
export const logEntries = pgTable("log_entries", {
  id: serial("id").primaryKey(),
  workflowRunId: integer("workflow_run_id").notNull().references(() => workflowRuns.id),
  nodeRunId: integer("node_run_id").references(() => nodeRuns.id),
  level: text("level").notNull(), // debug, info, warn, error
  message: text("message").notNull(),
  details: jsonb("details").notNull().default({}),
  timestamp: timestamp("timestamp").notNull().defaultNow(),
  source: text("source").notNull(), // workflow, node, system
  category: text("category"), // execution, validation, performance, etc.
});

export const insertLogEntrySchema = createInsertSchema(logEntries).pick({
  workflowRunId: true,
  nodeRunId: true,
  level: true,
  message: true,
  details: true,
  source: true,
  category: true,
});

// Custom Node Templates schema
export const customNodeTemplates = pgTable("custom_node_templates", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull().default(''),
  icon: text("icon").notNull().default('puzzle'),
  code: text("code").notNull(),
  inputs: jsonb("inputs").notNull().default([]),
  outputs: jsonb("outputs").notNull().default([]),
  isPublic: boolean("is_public").notNull().default(false),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const insertCustomNodeTemplateSchema = createInsertSchema(customNodeTemplates).pick({
  name: true,
  description: true,
  icon: true,
  code: true,
  inputs: true,
  outputs: true,
  isPublic: true,
});

// Export types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;

export type Workflow = typeof workflows.$inferSelect;
export type InsertWorkflow = z.infer<typeof insertWorkflowSchema>;

export type Credential = typeof credentials.$inferSelect;
export type InsertCredential = z.infer<typeof insertCredentialSchema>;

export type WorkflowRun = typeof workflowRuns.$inferSelect;
export type InsertWorkflowRun = z.infer<typeof insertWorkflowRunSchema>;

export type NodeRun = typeof nodeRuns.$inferSelect;
export type InsertNodeRun = z.infer<typeof insertNodeRunSchema>;

export type LogEntry = typeof logEntries.$inferSelect;
export type InsertLogEntry = z.infer<typeof insertLogEntrySchema>;

export type CustomNodeTemplate = typeof customNodeTemplates.$inferSelect;
export type InsertCustomNodeTemplate = z.infer<typeof insertCustomNodeTemplateSchema>;
