import { storage } from "./storage";
import type { InsertLogEntry } from "@shared/schema";

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error'
}

export enum LogSource {
  WORKFLOW = 'workflow',
  NODE = 'node',
  SYSTEM = 'system'
}

export enum LogCategory {
  EXECUTION = 'execution',
  VALIDATION = 'validation',
  PERFORMANCE = 'performance',
  ERROR = 'error',
  CONFIGURATION = 'configuration',
  RESOURCE = 'resource'
}

export interface LogContext {
  workflowRunId: number;
  nodeRunId?: number;
  workflowId?: number;
  nodeId?: string;
  nodeType?: string;
  executionPhase?: string;
}

export interface PerformanceMetrics {
  startTime: number;
  endTime?: number;
  duration?: number;
  memoryUsage?: number;
  cpuTime?: number;
}

export class WorkflowLogger {
  private context: LogContext;
  private performanceMetrics: Map<string, PerformanceMetrics> = new Map();

  constructor(context: LogContext) {
    this.context = context;
  }

  async debug(message: string, details: any = {}, category?: LogCategory): Promise<void> {
    await this.log(LogLevel.DEBUG, message, details, LogSource.WORKFLOW, category);
  }

  async info(message: string, details: any = {}, category?: LogCategory): Promise<void> {
    await this.log(LogLevel.INFO, message, details, LogSource.WORKFLOW, category);
  }

  async warn(message: string, details: any = {}, category?: LogCategory): Promise<void> {
    await this.log(LogLevel.WARN, message, details, LogSource.WORKFLOW, category);
  }

  async error(message: string, error?: Error, details: any = {}, category?: LogCategory): Promise<void> {
    const errorDetails = {
      ...details,
      ...(error && {
        errorName: error.name,
        errorMessage: error.message,
        stackTrace: error.stack,
      })
    };
    await this.log(LogLevel.ERROR, message, errorDetails, LogSource.WORKFLOW, category || LogCategory.ERROR);
  }

  async logNodeExecution(nodeId: string, nodeType: string, message: string, details: any = {}): Promise<void> {
    const nodeDetails = {
      ...details,
      nodeId,
      nodeType,
      executionPhase: this.context.executionPhase || 'execution'
    };
    await this.log(LogLevel.INFO, message, nodeDetails, LogSource.NODE, LogCategory.EXECUTION);
  }

  async logPerformance(metricName: string, metrics: PerformanceMetrics, details: any = {}): Promise<void> {
    const performanceDetails = {
      ...details,
      metricName,
      ...metrics,
      duration: metrics.duration || (metrics.endTime ? metrics.endTime - metrics.startTime : undefined)
    };
    await this.log(LogLevel.INFO, `Performance metric: ${metricName}`, performanceDetails, LogSource.SYSTEM, LogCategory.PERFORMANCE);
  }

  startPerformanceTimer(metricName: string): void {
    this.performanceMetrics.set(metricName, {
      startTime: Date.now()
    });
  }

  async endPerformanceTimer(metricName: string, details: any = {}): Promise<void> {
    const metric = this.performanceMetrics.get(metricName);
    if (metric) {
      metric.endTime = Date.now();
      metric.duration = metric.endTime - metric.startTime;
      await this.logPerformance(metricName, metric, details);
      this.performanceMetrics.delete(metricName);
    }
  }

  private async log(
    level: LogLevel,
    message: string,
    details: any,
    source: LogSource,
    category?: LogCategory
  ): Promise<void> {
    try {
      const logEntry: InsertLogEntry = {
        workflowRunId: this.context.workflowRunId,
        nodeRunId: this.context.nodeRunId,
        level,
        message,
        details: {
          ...details,
          context: {
            workflowId: this.context.workflowId,
            nodeId: this.context.nodeId,
            nodeType: this.context.nodeType,
            executionPhase: this.context.executionPhase,
            timestamp: new Date().toISOString()
          }
        },
        source,
        category
      };

      // Store in database
      await storage.createLogEntry(logEntry);

      // Also log to console for development
      const consoleMessage = `[${level.toUpperCase()}] [${source}] ${message}`;
      switch (level) {
        case LogLevel.DEBUG:
          console.debug(consoleMessage, details);
          break;
        case LogLevel.INFO:
          console.info(consoleMessage, details);
          break;
        case LogLevel.WARN:
          console.warn(consoleMessage, details);
          break;
        case LogLevel.ERROR:
          console.error(consoleMessage, details);
          break;
      }
    } catch (error) {
      // Fallback to console logging if database logging fails
      console.error('Failed to write log entry to database:', error);
      console.log(`[${level.toUpperCase()}] [${source}] ${message}`, details);
    }
  }

  createNodeLogger(nodeRunId: number, nodeId: string, nodeType: string): WorkflowLogger {
    return new WorkflowLogger({
      ...this.context,
      nodeRunId,
      nodeId,
      nodeType
    });
  }

  updateContext(updates: Partial<LogContext>): void {
    this.context = { ...this.context, ...updates };
  }
}

export class LoggingService {
  static createWorkflowLogger(workflowRunId: number, workflowId?: number): WorkflowLogger {
    return new WorkflowLogger({
      workflowRunId,
      workflowId
    });
  }

  static async logSystemEvent(message: string, details: any = {}, level: LogLevel = LogLevel.INFO): Promise<void> {
    try {
      console.log(`[SYSTEM] [${level.toUpperCase()}] ${message}`, details);
    } catch (error) {
      console.error('Failed to log system event:', error);
    }
  }
}
